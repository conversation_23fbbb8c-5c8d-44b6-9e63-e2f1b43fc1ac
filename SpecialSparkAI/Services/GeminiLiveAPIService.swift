//
//  GeminiLiveAPIService.swift
//  SpecialSparkAI
//
//  Gemini Live API Service for real-time voice and text interactions
//

import Foundation
import Network
import AVFoundation

@MainActor
class GeminiLiveAPIService: ObservableObject {
    static let shared = GeminiLiveAPIService()

    @Published var isConnected = false
    @Published var isRecording = false
    @Published var currentResponse = ""
    @Published var errorMessage: String?

    private var webSocketTask: URLSessionWebSocketTask?
    private var urlSession: URLSession?
    private var audioEngine: AVAudioEngine?
    private var inputNode: AVAudioInputNode?

    private let apiKey = APIConfig.Gemini.apiKey
    private let model = "gemini-2.0-flash-live-001"

    private init() {
        setupURLSession()
    }

    private func setupURLSession() {
        let config = URLSessionConfiguration.default
        config.timeoutIntervalForRequest = 30
        config.timeoutIntervalForResource = 300
        urlSession = URLSession(configuration: config)
    }

    // MARK: - Connection Management

    func connect(responseModality: ResponseModality = .text) async throws {
        guard !isConnected else { return }

        let url = buildWebSocketURL()
        guard let wsURL = URL(string: url) else {
            throw GeminiLiveAPIError.invalidURL
        }

        webSocketTask = urlSession?.webSocketTask(with: wsURL)
        webSocketTask?.resume()

        // Send setup configuration
        let config = LiveConnectConfig(
            responseModalities: [responseModality.rawValue],
            systemInstruction: createSystemInstruction()
        )

        try await sendSetupMessage(config: config)
        isConnected = true

        // Start listening for responses
        startListening()
    }

    func disconnect() {
        webSocketTask?.cancel(with: .goingAway, reason: nil)
        webSocketTask = nil
        isConnected = false
        stopAudioRecording()
    }

    private func buildWebSocketURL() -> String {
        return "wss://generativelanguage.googleapis.com/ws/google.ai.generativelanguage.v1alpha.GenerativeService.BidiGenerateContent?key=\(apiKey)"
    }

    // MARK: - Message Sending

    func sendTextMessage(_ text: String) async throws {
        guard isConnected else {
            throw GeminiLiveAPIError.notConnected
        }

        let message = ClientContentMessage(
            turns: [
                Turn(
                    role: "user",
                    parts: [Part(text: text)]
                )
            ],
            turnComplete: true
        )

        try await sendMessage(message)
    }

    func sendAudioData(_ audioData: Data) async throws {
        guard isConnected else {
            throw GeminiLiveAPIError.notConnected
        }

        let audioBlob = AudioBlob(
            data: audioData,
            mimeType: "audio/pcm;rate=16000"
        )

        let message = RealtimeInputMessage(audio: audioBlob)
        try await sendMessage(message)
    }

    private func sendSetupMessage(config: LiveConnectConfig) async throws {
        let setupMessage = SetupMessage(config: config)
        try await sendMessage(setupMessage)
    }

    private func sendMessage<T: Codable>(_ message: T) async throws {
        let encoder = JSONEncoder()
        let data = try encoder.encode(message)
        let wsMessage = URLSessionWebSocketTask.Message.data(data)

        try await webSocketTask?.send(wsMessage)
    }

    // MARK: - Response Handling

    private func startListening() {
        Task {
            while isConnected {
                do {
                    let message = try await webSocketTask?.receive()
                    await handleReceivedMessage(message)
                } catch {
                    await MainActor.run {
                        self.errorMessage = "Connection error: \(error.localizedDescription)"
                        self.isConnected = false
                    }
                    break
                }
            }
        }
    }

    private func handleReceivedMessage(_ message: URLSessionWebSocketTask.Message?) async {
        guard let message = message else { return }

        switch message {
        case .data(let data):
            await processResponseData(data)
        case .string(let string):
            if let data = string.data(using: .utf8) {
                await processResponseData(data)
            }
        @unknown default:
            break
        }
    }

    private func processResponseData(_ data: Data) async {
        do {
            let decoder = JSONDecoder()
            let response = try decoder.decode(ServerResponse.self, from: data)

            await MainActor.run {
                if let text = response.text {
                    self.currentResponse += text
                }

                if let error = response.error {
                    self.errorMessage = error.message
                }
            }
        } catch {
            await MainActor.run {
                self.errorMessage = "Failed to decode response: \(error.localizedDescription)"
            }
        }
    }

    // MARK: - Audio Recording

    func startAudioRecording() async throws {
        #if targetEnvironment(simulator)
        // Simulate audio recording in simulator
        isRecording = true
        return
        #else

        guard !isRecording else { return }

        try await setupAudioEngine()

        try audioEngine?.start()
        isRecording = true
        #endif
    }

    func stopAudioRecording() {
        #if targetEnvironment(simulator)
        isRecording = false
        return
        #else

        audioEngine?.stop()
        audioEngine?.inputNode.removeTap(onBus: 0)
        isRecording = false
        #endif
    }

    private func setupAudioEngine() async throws {
        audioEngine = AVAudioEngine()
        guard let audioEngine = audioEngine else { return }

        inputNode = audioEngine.inputNode
        let recordingFormat = inputNode?.outputFormat(forBus: 0)

        // Convert to 16kHz mono format required by Gemini Live API
        let targetFormat = AVAudioFormat(
            commonFormat: .pcmFormatInt16,
            sampleRate: 16000,
            channels: 1,
            interleaved: false
        )

        guard let targetFormat = targetFormat else {
            throw GeminiLiveAPIError.audioSetupFailed
        }

        inputNode?.installTap(onBus: 0, bufferSize: 1024, format: recordingFormat) { [weak self] buffer, _ in
            Task {
                await self?.processAudioBuffer(buffer, targetFormat: targetFormat)
            }
        }
    }

    private func processAudioBuffer(_ buffer: AVAudioPCMBuffer, targetFormat: AVAudioFormat) async {
        // Convert audio buffer to required format and send to API
        guard let audioData = convertBufferToData(buffer, targetFormat: targetFormat) else { return }

        do {
            try await sendAudioData(audioData)
        } catch {
            await MainActor.run {
                self.errorMessage = "Failed to send audio: \(error.localizedDescription)"
            }
        }
    }

    private func convertBufferToData(_ buffer: AVAudioPCMBuffer, targetFormat: AVAudioFormat) -> Data? {
        // Convert AVAudioPCMBuffer to Data in the required format
        // This is a simplified implementation - in production, you'd want proper audio conversion
        guard let channelData = buffer.int16ChannelData else { return nil }

        let frameLength = Int(buffer.frameLength)
        let data = Data(bytes: channelData[0], count: frameLength * 2) // 2 bytes per Int16

        return data
    }

    // MARK: - Helper Methods

    private func createSystemInstruction() -> Content {
        return Content(
            parts: [
                Part(text: """
                You are an AI teacher in SpecialSparkAI, a virtual school designed for students with special needs.
                You should be patient, encouraging, and adapt your teaching style to each student's needs.
                Use clear, simple language and provide positive reinforcement.
                Break down complex concepts into smaller, manageable steps.
                """)
            ]
        )
    }
}

// MARK: - Data Models

enum ResponseModality: String, Codable {
    case text = "TEXT"
    case audio = "AUDIO"
}

struct LiveConnectConfig: Codable {
    let responseModalities: [String]
    let systemInstruction: Content?

    enum CodingKeys: String, CodingKey {
        case responseModalities = "response_modalities"
        case systemInstruction = "system_instruction"
    }
}

struct Content: Codable {
    let parts: [Part]
}

struct Part: Codable {
    let text: String?

    init(text: String) {
        self.text = text
    }
}

struct Turn: Codable {
    let role: String
    let parts: [Part]
}

struct SetupMessage: Codable {
    let config: LiveConnectConfig

    enum CodingKeys: String, CodingKey {
        case config = "setup"
    }
}

struct ClientContentMessage: Codable {
    let turns: [Turn]
    let turnComplete: Bool

    enum CodingKeys: String, CodingKey {
        case turns = "client_content"
        case turnComplete = "turn_complete"
    }
}

struct RealtimeInputMessage: Codable {
    let audio: AudioBlob

    enum CodingKeys: String, CodingKey {
        case audio = "realtime_input"
    }
}

struct AudioBlob: Codable {
    let data: Data
    let mimeType: String

    enum CodingKeys: String, CodingKey {
        case data
        case mimeType = "mime_type"
    }
}

struct ServerResponse: Codable {
    let text: String?
    let error: APIError?
    let serverContent: ServerContent?

    enum CodingKeys: String, CodingKey {
        case text
        case error
        case serverContent = "server_content"
    }
}

struct ServerContent: Codable {
    let modelTurn: ModelTurn?
    let turnComplete: Bool?

    enum CodingKeys: String, CodingKey {
        case modelTurn = "model_turn"
        case turnComplete = "turn_complete"
    }
}

struct ModelTurn: Codable {
    let parts: [Part]
}

struct APIError: Codable {
    let message: String
    let code: Int?
}

enum GeminiLiveAPIError: Error, LocalizedError {
    case invalidURL
    case notConnected
    case audioSetupFailed
    case encodingFailed
    case decodingFailed

    var errorDescription: String? {
        switch self {
        case .invalidURL:
            return "Invalid WebSocket URL"
        case .notConnected:
            return "Not connected to Gemini Live API"
        case .audioSetupFailed:
            return "Failed to setup audio recording"
        case .encodingFailed:
            return "Failed to encode message"
        case .decodingFailed:
            return "Failed to decode response"
        }
    }
}
