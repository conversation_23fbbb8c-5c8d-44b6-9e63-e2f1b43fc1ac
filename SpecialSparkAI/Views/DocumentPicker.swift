//
//  DocumentPicker.swift
//  SpecialSparkAI
//
//  Created by MAGESH DHANASEKARAN on 5/23/25.
//

import SwiftUI
import UniformTypeIdentifiers

struct DocumentPicker: UIViewControllerRepresentable {
    @Binding var selectedFiles: [URL]
    @Environment(\.dismiss) private var dismiss
    
    func makeUIViewController(context: Context) -> UIDocumentPickerViewController {
        let picker = UIDocumentPickerViewController(forOpeningContentTypes: [
            .pdf,
            .plainText,
            .rtf,
            .image,
            .audio,
            .video,
            .data
        ], asCopy: true)
        
        picker.allowsMultipleSelection = true
        picker.delegate = context.coordinator
        return picker
    }
    
    func updateUIViewController(_ uiViewController: UIDocumentPickerViewController, context: Context) {}
    
    func makeCoordinator() -> Coordinator {
        Coordinator(self)
    }
    
    class Coordinator: NSObject, UIDocumentPickerDelegate {
        let parent: DocumentPicker
        
        init(_ parent: DocumentPicker) {
            self.parent = parent
        }
        
        func documentPicker(_ controller: UIDocumentPickerViewController, didPickDocumentsAt urls: [URL]) {
            parent.selectedFiles.append(contentsOf: urls)
            parent.dismiss()
        }
        
        func documentPickerWasCancelled(_ controller: UIDocumentPickerViewController) {
            parent.dismiss()
        }
    }
}

struct KnowledgeBaseView: View {
    let agent: AIAgent
    @StateObject private var supabaseService = SupabaseService.shared
    @State private var uploadedFiles: [KnowledgeBaseFile] = []
    @State private var isLoading = false
    @State private var showingFilePicker = false
    @State private var selectedFiles: [URL] = []
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // Header
                knowledgeBaseHeader
                
                // Files list
                if uploadedFiles.isEmpty && !isLoading {
                    emptyStateView
                } else {
                    filesListView
                }
                
                Spacer()
                
                // Upload button
                uploadButton
            }
            .navigationTitle("\(agent.name)'s Knowledge Base")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Done") {
                        dismiss()
                    }
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Add Files") {
                        showingFilePicker = true
                    }
                }
            }
            .onAppear {
                loadKnowledgeBase()
            }
            .sheet(isPresented: $showingFilePicker) {
                DocumentPicker(selectedFiles: $selectedFiles)
            }
            .onChange(of: selectedFiles) { oldValue, newValue in
                if !newValue.isEmpty {
                    uploadFiles()
                }
            }
        }
    }
    
    private var knowledgeBaseHeader: some View {
        VStack(spacing: 16) {
            HStack(spacing: 12) {
                Image(systemName: "brain.head.profile")
                    .font(.title2)
                    .foregroundColor(agent.agentType.color)
                
                VStack(alignment: .leading, spacing: 4) {
                    Text("Knowledge Base")
                        .font(.headline)
                        .fontWeight(.bold)
                    
                    Text("Files and documents for \(agent.name)")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                
                Spacer()
                
                Text("\(uploadedFiles.count) files")
                    .font(.caption)
                    .padding(.horizontal, 8)
                    .padding(.vertical, 4)
                    .background(agent.agentType.color.opacity(0.1))
                    .foregroundColor(agent.agentType.color)
                    .cornerRadius(8)
            }
            .padding()
            .background(.ultraThinMaterial)
        }
    }
    
    private var emptyStateView: some View {
        VStack(spacing: 20) {
            Image(systemName: "doc.text")
                .font(.system(size: 60))
                .foregroundColor(.secondary)
            
            VStack(spacing: 8) {
                Text("No Files Yet")
                    .font(.title2)
                    .fontWeight(.semibold)
                
                Text("Upload documents, images, or other files to enhance \(agent.name)'s knowledge and capabilities.")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
                    .padding(.horizontal)
            }
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
    }
    
    private var filesListView: some View {
        ScrollView {
            LazyVStack(spacing: 12) {
                ForEach(uploadedFiles) { file in
                    KnowledgeBaseFileRow(file: file, agentColor: agent.agentType.color) {
                        deleteFile(file)
                    }
                }
            }
            .padding()
        }
    }
    
    private var uploadButton: some View {
        Button(action: { showingFilePicker = true }) {
            HStack(spacing: 8) {
                Image(systemName: "plus.circle.fill")
                    .font(.title3)
                
                Text("Add Files to Knowledge Base")
                    .font(.headline)
                    .fontWeight(.semibold)
            }
            .foregroundColor(.white)
            .frame(maxWidth: .infinity)
            .padding()
            .background(
                LinearGradient(
                    colors: [agent.agentType.color, agent.agentType.color.opacity(0.8)],
                    startPoint: .leading,
                    endPoint: .trailing
                )
            )
            .cornerRadius(16)
            .shadow(color: agent.agentType.color.opacity(0.3), radius: 4, x: 0, y: 2)
        }
        .padding()
        .disabled(isLoading)
    }
    
    private func loadKnowledgeBase() {
        // TODO: Load files from Supabase for this agent
        isLoading = true
        
        Task {
            // Simulate loading
            try? await Task.sleep(nanoseconds: 1_000_000_000)
            
            await MainActor.run {
                self.isLoading = false
                // Load actual files here
            }
        }
    }
    
    private func uploadFiles() {
        guard !selectedFiles.isEmpty else { return }
        
        isLoading = true
        
        Task {
            for fileURL in selectedFiles {
                let file = KnowledgeBaseFile(
                    id: UUID(),
                    agentId: agent.id,
                    fileName: fileURL.lastPathComponent,
                    fileType: fileURL.pathExtension,
                    fileSize: getFileSize(url: fileURL),
                    uploadDate: Date(),
                    isProcessed: false
                )
                
                // TODO: Upload to Supabase storage
                await MainActor.run {
                    self.uploadedFiles.append(file)
                }
            }
            
            await MainActor.run {
                self.selectedFiles.removeAll()
                self.isLoading = false
            }
        }
    }
    
    private func deleteFile(_ file: KnowledgeBaseFile) {
        uploadedFiles.removeAll { $0.id == file.id }
        // TODO: Delete from Supabase
    }
    
    private func getFileSize(url: URL) -> Int64 {
        do {
            let resources = try url.resourceValues(forKeys: [.fileSizeKey])
            return Int64(resources.fileSize ?? 0)
        } catch {
            return 0
        }
    }
}

struct KnowledgeBaseFile: Identifiable, Codable {
    let id: UUID
    let agentId: UUID
    let fileName: String
    let fileType: String
    let fileSize: Int64
    let uploadDate: Date
    var isProcessed: Bool
}

struct KnowledgeBaseFileRow: View {
    let file: KnowledgeBaseFile
    let agentColor: Color
    let onDelete: () -> Void
    
    var body: some View {
        HStack(spacing: 12) {
            // File icon
            Image(systemName: fileIcon(for: file.fileType))
                .font(.title2)
                .foregroundColor(agentColor)
                .frame(width: 40, height: 40)
                .background(agentColor.opacity(0.1))
                .cornerRadius(8)
            
            // File info
            VStack(alignment: .leading, spacing: 4) {
                Text(file.fileName)
                    .font(.subheadline)
                    .fontWeight(.medium)
                    .lineLimit(1)
                
                HStack(spacing: 8) {
                    Text(formatFileSize(file.fileSize))
                        .font(.caption)
                        .foregroundColor(.secondary)
                    
                    Text("•")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    
                    Text(file.uploadDate.formatted(date: .abbreviated, time: .omitted))
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                
                if file.isProcessed {
                    HStack(spacing: 4) {
                        Image(systemName: "checkmark.circle.fill")
                            .font(.caption)
                            .foregroundColor(.green)
                        
                        Text("Processed")
                            .font(.caption)
                            .foregroundColor(.green)
                    }
                } else {
                    HStack(spacing: 4) {
                        ProgressView()
                            .scaleEffect(0.7)
                        
                        Text("Processing...")
                            .font(.caption)
                            .foregroundColor(.orange)
                    }
                }
            }
            
            Spacer()
            
            // Delete button
            Button(action: onDelete) {
                Image(systemName: "trash")
                    .font(.caption)
                    .foregroundColor(.red)
            }
        }
        .padding()
        .background(.ultraThinMaterial)
        .cornerRadius(12)
    }
    
    private func fileIcon(for fileType: String) -> String {
        switch fileType.lowercased() {
        case "pdf": return "doc.text.fill"
        case "txt", "rtf": return "doc.plaintext.fill"
        case "jpg", "jpeg", "png", "gif": return "photo.fill"
        case "mp3", "wav", "m4a": return "music.note"
        case "mp4", "mov", "avi": return "video.fill"
        default: return "doc.fill"
        }
    }
    
    private func formatFileSize(_ bytes: Int64) -> String {
        let formatter = ByteCountFormatter()
        formatter.allowedUnits = [.useKB, .useMB, .useGB]
        formatter.countStyle = .file
        return formatter.string(fromByteCount: bytes)
    }
}
