//
//  SettingsView.swift
//  SpecialSparkAI
//
//  Created by MAGESH DHANASEKARAN on 5/23/25.
//

import SwiftUI
import SwiftData

struct SettingsView: View {
    @Environment(\.modelContext) private var modelContext
    @StateObject private var settingsManager = SettingsManager.shared
    @State private var showingResetAlert = false
    @State private var showingAbout = false

    var body: some View {
        NavigationView {
            List {
                // Profile Section
                profileSection

                // Accessibility & Sensory Settings
                accessibilitySection

                // Learning Preferences
                learningSection

                // Notifications
                notificationsSection

                // Privacy & Security
                privacySection

                // Support & Help
                supportSection

                // About
                aboutSection
            }
            .navigationTitle("Settings")
            .navigationBarTitleDisplayMode(.large)
        }
        .alert("Reset All Settings", isPresented: $showingResetAlert) {
            Button("Cancel", role: .cancel) { }
            Button("Reset", role: .destructive) {
                settingsManager.resetToDefaults()
            }
        } message: {
            Text("This will reset all settings to their default values. This action cannot be undone.")
        }
        .sheet(isPresented: $showingAbout) {
            AboutView()
        }
    }

    // MARK: - Profile Section
    private var profileSection: some View {
        Section("Profile") {
            NavigationLink(destination: StudentProfileEditView()) {
                HStack {
                    Image(systemName: "person.circle.fill")
                        .foregroundColor(.blue)
                        .font(.title2)

                    VStack(alignment: .leading) {
                        Text("Edit Profile")
                            .font(.headline)
                        Text("Update your personal information")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                }
            }

            NavigationLink(destination: GradeSelectionView()) {
                HStack {
                    Image(systemName: "graduationcap.fill")
                        .foregroundColor(.purple)
                        .font(.title2)

                    VStack(alignment: .leading) {
                        Text("Grade Level")
                            .font(.headline)
                        Text("Current: \(settingsManager.currentGrade)")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                }
            }
        }
    }

    // MARK: - Accessibility & Sensory Settings
    private var accessibilitySection: some View {
        Section("Accessibility & Sensory") {
            NavigationLink(destination: EnhancedSensorySettingsView()) {
                HStack {
                    Image(systemName: "eye.fill")
                        .foregroundColor(.mint)
                        .font(.title2)

                    VStack(alignment: .leading) {
                        Text("Sensory Settings")
                            .font(.headline)
                        Text("Visual, audio, and motion preferences")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                }
            }

            Toggle(isOn: $settingsManager.highContrastMode) {
                HStack {
                    Image(systemName: "circle.lefthalf.filled")
                        .foregroundColor(.primary)
                        .font(.title2)

                    VStack(alignment: .leading) {
                        Text("High Contrast")
                        Text("Easier to see interface elements")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                }
            }

            Toggle(isOn: $settingsManager.reduceMotion) {
                HStack {
                    Image(systemName: "figure.walk.motion")
                        .foregroundColor(.orange)
                        .font(.title2)

                    VStack(alignment: .leading) {
                        Text("Reduce Motion")
                        Text("Minimize animations and effects")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                }
            }

            Toggle(isOn: $settingsManager.largeText) {
                HStack {
                    Image(systemName: "textformat.size")
                        .foregroundColor(.blue)
                        .font(.title2)

                    VStack(alignment: .leading) {
                        Text("Large Text")
                        Text("Increase text size for better readability")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                }
            }
        }
    }

    // MARK: - Learning Preferences
    private var learningSection: some View {
        Section("Learning Preferences") {
            NavigationLink(destination: LearningStyleView()) {
                HStack {
                    Image(systemName: "brain.head.profile")
                        .foregroundColor(.pink)
                        .font(.title2)

                    VStack(alignment: .leading) {
                        Text("Learning Style")
                            .font(.headline)
                        Text("Visual, auditory, kinesthetic preferences")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                }
            }

            NavigationLink(destination: DifficultySettingsView()) {
                HStack {
                    Image(systemName: "slider.horizontal.3")
                        .foregroundColor(.green)
                        .font(.title2)

                    VStack(alignment: .leading) {
                        Text("Difficulty Level")
                            .font(.headline)
                        Text("Adjust challenge level for subjects")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                }
            }

            Toggle(isOn: $settingsManager.adaptiveLearning) {
                HStack {
                    Image(systemName: "brain.filled.head.profile")
                        .foregroundColor(.purple)
                        .font(.title2)

                    VStack(alignment: .leading) {
                        Text("Adaptive Learning")
                        Text("AI adjusts to your learning pace")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                }
            }

            NavigationLink(destination: KnowledgeBaseManagementView()) {
                HStack {
                    Image(systemName: "brain.head.profile")
                        .foregroundColor(.indigo)
                        .font(.title2)

                    VStack(alignment: .leading) {
                        Text("Knowledge Base")
                            .font(.headline)
                        Text("Manage AI teacher knowledge files")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                }
            }
        }
    }

    // MARK: - Notifications
    private var notificationsSection: some View {
        Section("Notifications") {
            Toggle(isOn: $settingsManager.pushNotifications) {
                HStack {
                    Image(systemName: "bell.fill")
                        .foregroundColor(.red)
                        .font(.title2)

                    VStack(alignment: .leading) {
                        Text("Push Notifications")
                        Text("Receive updates and reminders")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                }
            }

            Toggle(isOn: $settingsManager.achievementNotifications) {
                HStack {
                    Image(systemName: "trophy.fill")
                        .foregroundColor(.yellow)
                        .font(.title2)

                    VStack(alignment: .leading) {
                        Text("Achievement Alerts")
                        Text("Celebrate your accomplishments")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                }
            }

            Toggle(isOn: $settingsManager.reminderNotifications) {
                HStack {
                    Image(systemName: "clock.fill")
                        .foregroundColor(.blue)
                        .font(.title2)

                    VStack(alignment: .leading) {
                        Text("Study Reminders")
                        Text("Gentle nudges to keep learning")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                }
            }
        }
    }

    // MARK: - Privacy Section
    private var privacySection: some View {
        Section("Privacy & Security") {
            NavigationLink(destination: PrivacySettingsView()) {
                HStack {
                    Image(systemName: "lock.shield.fill")
                        .foregroundColor(.indigo)
                        .font(.title2)

                    VStack(alignment: .leading) {
                        Text("Privacy Settings")
                            .font(.headline)
                        Text("Control your data and privacy")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                }
            }

            NavigationLink(destination: DataManagementView()) {
                HStack {
                    Image(systemName: "externaldrive.fill")
                        .foregroundColor(.teal)
                        .font(.title2)

                    VStack(alignment: .leading) {
                        Text("Data Management")
                            .font(.headline)
                        Text("Export, backup, or delete your data")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                }
            }
        }
    }

    // MARK: - Support Section
    private var supportSection: some View {
        Section("Support & Help") {
            NavigationLink(destination: HelpCenterView()) {
                HStack {
                    Image(systemName: "questionmark.circle.fill")
                        .foregroundColor(.blue)
                        .font(.title2)

                    VStack(alignment: .leading) {
                        Text("Help Center")
                            .font(.headline)
                        Text("FAQs and tutorials")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                }
            }

            NavigationLink(destination: ContactSupportView()) {
                HStack {
                    Image(systemName: "envelope.fill")
                        .foregroundColor(.green)
                        .font(.title2)

                    VStack(alignment: .leading) {
                        Text("Contact Support")
                            .font(.headline)
                        Text("Get help from our team")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                }
            }

            Button(action: { showingResetAlert = true }) {
                HStack {
                    Image(systemName: "arrow.clockwise.circle.fill")
                        .foregroundColor(.orange)
                        .font(.title2)

                    VStack(alignment: .leading) {
                        Text("Reset Settings")
                            .font(.headline)
                        Text("Restore default settings")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }

                    Spacer()
                }
            }
            .foregroundColor(.primary)
        }
    }

    // MARK: - About Section
    private var aboutSection: some View {
        Section("About") {
            Button(action: { showingAbout = true }) {
                HStack {
                    Image(systemName: "info.circle.fill")
                        .foregroundColor(.blue)
                        .font(.title2)

                    VStack(alignment: .leading) {
                        Text("About SpecialSpark AI")
                            .font(.headline)
                        Text("Version 1.0.0")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }

                    Spacer()
                }
            }
            .foregroundColor(.primary)
        }
    }
}

// MARK: - Placeholder Views
struct StudentProfileEditView: View {
    var body: some View {
        Text("Student Profile Edit")
            .navigationTitle("Edit Profile")
    }
}



struct LearningStyleView: View {
    var body: some View {
        Text("Learning Style Settings")
            .navigationTitle("Learning Style")
    }
}

struct DifficultySettingsView: View {
    var body: some View {
        Text("Difficulty Settings")
            .navigationTitle("Difficulty Level")
    }
}

struct PrivacySettingsView: View {
    var body: some View {
        Text("Privacy Settings")
            .navigationTitle("Privacy")
    }
}

struct DataManagementView: View {
    var body: some View {
        Text("Data Management")
            .navigationTitle("Data Management")
    }
}

struct HelpCenterView: View {
    var body: some View {
        Text("Help Center")
            .navigationTitle("Help Center")
    }
}

struct ContactSupportView: View {
    var body: some View {
        Text("Contact Support")
            .navigationTitle("Contact Support")
    }
}

struct AboutView: View {
    var body: some View {
        NavigationView {
            VStack(spacing: 20) {
                Image(systemName: "sparkles")
                    .font(.system(size: 80))
                    .foregroundColor(.blue)

                Text("SpecialSpark AI")
                    .font(.largeTitle)
                    .fontWeight(.bold)

                Text("Empowering every learner with AI")
                    .font(.headline)
                    .foregroundColor(.secondary)

                Spacer()
            }
            .padding()
            .navigationTitle("About")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") {
                        // Dismiss
                    }
                }
            }
        }
    }
}
