//
//  ArtCenterView.swift
//  SpecialSparkAI
//
//  Created by MAGESH DHANASEKARAN on 5/23/25.
//

import SwiftUI

struct ArtCenterView: View {
    let gradeLevel: GradeLevel
    @StateObject private var lessonDataService = LessonDataService()
    @State private var selectedArtForm: ArtForm?
    @State private var selectedProject: ArtProject?
    @State private var showingProjectDetail = false
    @Environment(\.dismiss) private var dismiss

    private var filteredProjects: [ArtProject] {
        let gradeProjects = lessonDataService.getArtProjectsForGrade(gradeLevel)

        if let artForm = selectedArtForm {
            return gradeProjects.filter { $0.artForm == artForm }
        }

        return gradeProjects
    }

    private var availableArtForms: [ArtForm] {
        let projects = lessonDataService.getArtProjectsForGrade(gradeLevel)
        return Array(Set(projects.map { $0.artForm })).sorted { $0.displayName < $1.displayName }
    }

    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // Header
                headerView

                // Art Form Filter
                artFormFilterView

                // Content
                ScrollView {
                    VStack(spacing: 20) {
                        // Featured Projects
                        featuredProjectsSection

                        // All Projects
                        allProjectsSection

                        // Art Gallery
                        artGallerySection

                        // Creative Inspiration
                        inspirationSection
                    }
                    .padding()
                }
            }
            .navigationTitle("Creative Arts Center")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") {
                        dismiss()
                    }
                }
            }
        }
        .sheet(isPresented: $showingProjectDetail) {
            if let project = selectedProject {
                ArtProjectDetailView(project: project, gradeLevel: gradeLevel)
            }
        }
    }

    private var headerView: some View {
        VStack(spacing: 12) {
            Text("Grade \(gradeLevel.displayName) Art Projects")
                .font(.title2)
                .fontWeight(.semibold)

            Text("Express your creativity through art and design")
                .font(.subheadline)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)

            // Art stats
            HStack(spacing: 20) {
                ArtStatView(title: "Projects", value: "\(filteredProjects.count)", icon: "paintbrush")
                ArtStatView(title: "Art Forms", value: "\(availableArtForms.count)", icon: "palette")
                ArtStatView(title: "Skill Level", value: gradeLevel.displayName, icon: "star")
            }
        }
        .padding()
        .background(Color(.systemGray6))
    }

    private var artFormFilterView: some View {
        ScrollView(.horizontal, showsIndicators: false) {
            HStack(spacing: 12) {
                ArtFormFilterButton(
                    title: "All",
                    icon: "rectangle.3.group",
                    isSelected: selectedArtForm == nil
                ) {
                    selectedArtForm = nil
                }

                ForEach(availableArtForms, id: \.self) { artForm in
                    ArtFormFilterButton(
                        title: artForm.displayName,
                        icon: artForm.icon,
                        isSelected: selectedArtForm == artForm
                    ) {
                        selectedArtForm = selectedArtForm == artForm ? nil : artForm
                    }
                }
            }
            .padding(.horizontal)
        }
        .padding(.vertical)
    }

    private var featuredProjectsSection: some View {
        VStack(alignment: .leading, spacing: 15) {
            HStack {
                Text("Featured Projects")
                    .font(.headline)
                    .fontWeight(.semibold)

                Spacer()

                Image(systemName: "star.fill")
                    .foregroundColor(.yellow)
            }

            ScrollView(.horizontal, showsIndicators: false) {
                HStack(spacing: 15) {
                    ForEach(filteredProjects.prefix(3)) { project in
                        FeaturedArtProjectCard(project: project) {
                            selectedProject = project
                            showingProjectDetail = true
                        }
                    }
                }
                .padding(.horizontal)
            }
        }
    }

    private var allProjectsSection: some View {
        VStack(alignment: .leading, spacing: 15) {
            HStack {
                Text("All Projects")
                    .font(.headline)
                    .fontWeight(.semibold)

                Spacer()

                Text("\(filteredProjects.count) projects")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }

            LazyVGrid(columns: [
                GridItem(.flexible()),
                GridItem(.flexible())
            ], spacing: 15) {
                ForEach(filteredProjects) { project in
                    ArtProjectCard(project: project) {
                        selectedProject = project
                        showingProjectDetail = true
                    }
                }
            }
        }
    }

    private var artGallerySection: some View {
        VStack(alignment: .leading, spacing: 15) {
            Text("Student Art Gallery")
                .font(.headline)
                .fontWeight(.semibold)

            ScrollView(.horizontal, showsIndicators: false) {
                HStack(spacing: 15) {
                    ForEach(0..<5) { index in
                        GalleryArtworkCard(
                            title: "Student Artwork \(index + 1)",
                            artist: "Grade \(gradeLevel.displayName) Student",
                            artForm: availableArtForms.randomElement() ?? .drawing
                        )
                    }
                }
                .padding(.horizontal)
            }
        }
    }

    private var inspirationSection: some View {
        VStack(alignment: .leading, spacing: 15) {
            Text("Creative Inspiration")
                .font(.headline)
                .fontWeight(.semibold)

            VStack(spacing: 12) {
                InspirationCard(
                    title: "Daily Art Challenge",
                    description: "Create something new every day",
                    icon: "calendar",
                    color: .blue
                )

                InspirationCard(
                    title: "Famous Artists Study",
                    description: "Learn from the masters",
                    icon: "person.crop.circle",
                    color: .purple
                )

                InspirationCard(
                    title: "Nature Sketching",
                    description: "Draw inspiration from nature",
                    icon: "leaf",
                    color: .green
                )
            }
        }
    }
}

// MARK: - Supporting Views

struct ArtStatView: View {
    let title: String
    let value: String
    let icon: String

    var body: some View {
        VStack(spacing: 6) {
            Image(systemName: icon)
                .font(.title3)
                .foregroundColor(.pink)

            Text(value)
                .font(.headline)
                .fontWeight(.bold)

            Text(title)
                .font(.caption)
                .foregroundColor(.secondary)
        }
        .frame(maxWidth: .infinity)
    }
}

struct ArtFormFilterButton: View {
    let title: String
    let icon: String
    let isSelected: Bool
    let action: () -> Void

    var body: some View {
        Button(action: action) {
            HStack(spacing: 6) {
                Image(systemName: icon)
                    .font(.caption)

                Text(title)
                    .font(.caption)
                    .fontWeight(.medium)
            }
            .foregroundColor(isSelected ? .white : .primary)
            .padding(.horizontal, 12)
            .padding(.vertical, 6)
            .background(isSelected ? .pink : Color(.systemGray5))
            .cornerRadius(15)
        }
    }
}

struct FeaturedArtProjectCard: View {
    let project: ArtProject
    let action: () -> Void

    var body: some View {
        Button(action: action) {
            VStack(spacing: 12) {
                // Project preview
                RoundedRectangle(cornerRadius: 8)
                    .fill(LinearGradient(
                        colors: [project.artForm.color.opacity(0.3), project.artForm.color.opacity(0.6)],
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    ))
                    .frame(width: 120, height: 90)
                    .overlay(
                        VStack {
                            Image(systemName: project.artForm.icon)
                                .foregroundColor(.white)
                                .font(.title2)

                            Text(project.artForm.displayName)
                                .font(.caption2)
                                .foregroundColor(.white)
                                .fontWeight(.medium)
                        }
                    )

                // Project info
                VStack(spacing: 4) {
                    Text(project.title)
                        .font(.caption)
                        .fontWeight(.semibold)
                        .multilineTextAlignment(.center)
                        .lineLimit(2)

                    HStack {
                        Label("\(project.duration) min", systemImage: "clock")
                        Label(project.difficulty.displayName, systemImage: "chart.bar")
                    }
                    .font(.caption2)
                    .foregroundColor(.secondary)
                }
            }
            .frame(width: 140)
        }
        .buttonStyle(PlainButtonStyle())
    }
}

struct ArtProjectCard: View {
    let project: ArtProject
    let action: () -> Void

    var body: some View {
        Button(action: action) {
            VStack(spacing: 12) {
                // Project preview
                RoundedRectangle(cornerRadius: 8)
                    .fill(LinearGradient(
                        colors: [project.artForm.color.opacity(0.3), project.artForm.color.opacity(0.6)],
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    ))
                    .frame(height: 100)
                    .overlay(
                        Image(systemName: project.artForm.icon)
                            .foregroundColor(.white)
                            .font(.title2)
                    )

                // Project info
                VStack(spacing: 4) {
                    Text(project.title)
                        .font(.subheadline)
                        .fontWeight(.semibold)
                        .multilineTextAlignment(.center)
                        .lineLimit(2)

                    Text(project.description)
                        .font(.caption)
                        .foregroundColor(.secondary)
                        .multilineTextAlignment(.center)
                        .lineLimit(2)

                    HStack {
                        Text(project.artForm.displayName)
                            .font(.caption2)
                            .foregroundColor(project.artForm.color)

                        Spacer()

                        Text("\(project.duration) min")
                            .font(.caption2)
                            .foregroundColor(.secondary)
                    }
                }
            }
            .padding()
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(.ultraThinMaterial)
                    .shadow(radius: 2)
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
}

struct GalleryArtworkCard: View {
    let title: String
    let artist: String
    let artForm: ArtForm

    var body: some View {
        VStack(spacing: 8) {
            // Artwork preview
            RoundedRectangle(cornerRadius: 8)
                .fill(LinearGradient(
                    colors: [artForm.color.opacity(0.4), artForm.color.opacity(0.7)],
                    startPoint: .topLeading,
                    endPoint: .bottomTrailing
                ))
                .frame(width: 100, height: 100)
                .overlay(
                    Image(systemName: artForm.icon)
                        .foregroundColor(.white)
                        .font(.title2)
                )

            // Artwork info
            VStack(spacing: 2) {
                Text(title)
                    .font(.caption)
                    .fontWeight(.semibold)
                    .lineLimit(1)

                Text("by \(artist)")
                    .font(.caption2)
                    .foregroundColor(.secondary)
                    .lineLimit(1)
            }
        }
        .frame(width: 100)
    }
}

struct InspirationCard: View {
    let title: String
    let description: String
    let icon: String
    let color: Color

    var body: some View {
        HStack(spacing: 15) {
            Image(systemName: icon)
                .font(.title2)
                .foregroundColor(color)
                .frame(width: 40)

            VStack(alignment: .leading, spacing: 4) {
                Text(title)
                    .font(.subheadline)
                    .fontWeight(.semibold)

                Text(description)
                    .font(.caption)
                    .foregroundColor(.secondary)
            }

            Spacer()

            Image(systemName: "chevron.right")
                .font(.caption)
                .foregroundColor(.secondary)
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(.ultraThinMaterial)
                .shadow(radius: 2)
        )
    }
}

// Extension for ArtForm colors
extension ArtForm {
    var color: Color {
        switch self {
        case .drawing: return .blue
        case .painting: return .red
        case .sculpture: return .brown
        case .printmaking: return .purple
        case .photography: return .gray
        case .digitalArt: return .cyan
        case .mixedMedia: return .purple
        case .ceramics: return .orange
        case .textiles: return .pink
        case .music: return .green
        case .dance: return .yellow
        case .theater: return .indigo
        case .creative_writing: return .teal
        case .filmmaking: return .black
        case .animation: return .mint
        }
    }
}



#Preview {
    ArtCenterView(gradeLevel: .grade5)
}
