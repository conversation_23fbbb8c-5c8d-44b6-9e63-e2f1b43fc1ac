//
//  EnhancedAgentChatView.swift
//  SpecialSparkAI
//
//  Created by MAGESH DHANASEKARAN on 5/23/25.
//

import SwiftUI
import AVFoundation
import UniformTypeIdentifiers

struct EnhancedAgentChatView: View {
    let agent: AIAgent
    @StateObject private var conversationManager = ConversationManager.shared
    @StateObject private var mockAuthService = MockAuthService.shared
    @StateObject private var supabaseService = SupabaseService.shared
    @StateObject private var geminiService = GeminiService.shared

    @State private var messageText = ""
    @State private var conversation: Conversation?
    @State private var isLoading = false
    @State private var showingError = false
    @State private var errorMessage = ""
    @State private var isRecording = false
    @State private var showingAttachments = false
    @State private var showingKnowledgeBase = false
    @State private var selectedFiles: [URL] = []
    @State private var audioRecorder: AVAudioRecorder?
    @State private var audioPlayer: AVAudioPlayer?
    @State private var recordingURL: URL?

    @Environment(\.dismiss) private var dismiss

    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // Enhanced Agent Header
                enhancedAgentHeader

                // Messages List with animations
                animatedMessagesScrollView

                // Enhanced Input Area
                enhancedInputArea
            }
            .navigationTitle("Chat with \(agent.name)")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Done") {
                        dismiss()
                    }
                }

                ToolbarItem(placement: .navigationBarTrailing) {
                    Menu {
                        Button("Knowledge Base", systemImage: "brain.head.profile") {
                            showingKnowledgeBase = true
                        }

                        Button("Clear Chat", systemImage: "trash") {
                            clearConversation()
                        }
                        .foregroundColor(.red)

                        Button("Agent Info", systemImage: "info.circle") {
                            // Show agent details
                        }
                    } label: {
                        Image(systemName: "ellipsis.circle")
                    }
                }
            }
            .onAppear {
                setupAudio()
                startConversation()
            }
            .alert("Error", isPresented: $showingError) {
                Button("OK") { }
            } message: {
                Text(errorMessage)
            }
            .sheet(isPresented: $showingAttachments) {
                DocumentPicker(selectedFiles: $selectedFiles)
            }
            .sheet(isPresented: $showingKnowledgeBase) {
                KnowledgeBaseView(agent: agent)
            }
        }
    }

    // MARK: - Enhanced Agent Header
    private var enhancedAgentHeader: some View {
        VStack(spacing: 12) {
            HStack(spacing: 15) {
                // Animated agent avatar
                ZStack {
                    Circle()
                        .fill(
                            LinearGradient(
                                colors: [agent.agentType.color.opacity(0.3), agent.agentType.color],
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            )
                        )
                        .frame(width: 60, height: 60)
                        .scaleEffect(1.0)
                        .animation(.easeInOut(duration: 2).repeatForever(autoreverses: true), value: true)

                    Image(systemName: agent.agentType.icon)
                        .font(.title2)
                        .foregroundColor(.white)
                }

                VStack(alignment: .leading, spacing: 4) {
                    Text(agent.name)
                        .font(.headline)
                        .fontWeight(.bold)
                        .foregroundStyle(
                            LinearGradient(
                                colors: [.primary, agent.agentType.color],
                                startPoint: .leading,
                                endPoint: .trailing
                            )
                        )

                    Text(agent.specialization)
                        .font(.subheadline)
                        .foregroundColor(.secondary)

                    HStack(spacing: 8) {
                        Circle()
                            .fill(.green)
                            .frame(width: 8, height: 8)
                            .scaleEffect(1.0)
                            .animation(.easeInOut(duration: 1).repeatForever(autoreverses: true), value: true)

                        Text("Online")
                            .font(.caption)
                            .foregroundColor(.green)
                    }
                }

                Spacer()

                // Voice interaction button
                Button(action: toggleVoiceRecording) {
                    Image(systemName: isRecording ? "mic.fill" : "mic")
                        .font(.title2)
                        .foregroundColor(isRecording ? .red : .blue)
                        .scaleEffect(isRecording ? 1.2 : 1.0)
                        .animation(.spring(response: 0.3, dampingFraction: 0.6), value: isRecording)
                }
                .disabled(isLoading)
            }
            .padding()

            // Agent capabilities
            ScrollView(.horizontal, showsIndicators: false) {
                HStack(spacing: 8) {
                    ForEach(agent.tools, id: \.self) { tool in
                        Text(tool.replacingOccurrences(of: "_", with: " ").capitalized)
                            .font(.caption)
                            .padding(.horizontal, 8)
                            .padding(.vertical, 4)
                            .background(agent.agentType.color.opacity(0.1))
                            .foregroundColor(agent.agentType.color)
                            .cornerRadius(8)
                    }
                }
                .padding(.horizontal)
            }
        }
        .background(.ultraThinMaterial)
        .overlay(
            Rectangle()
                .fill(agent.agentType.color.opacity(0.2))
                .frame(height: 1),
            alignment: .bottom
        )
    }

    // MARK: - Animated Messages Scroll View
    private var animatedMessagesScrollView: some View {
        ScrollViewReader { proxy in
            ScrollView {
                LazyVStack(spacing: 16) {
                    if let conversation = conversation {
                        ForEach(Array(conversation.messages.enumerated()), id: \.element.id) { index, message in
                            EnhancedMessageBubble(
                                message: message,
                                agentName: agent.name,
                                agentType: agent.agentType
                            )
                            .id(message.id)
                            .transition(.asymmetric(
                                insertion: .move(edge: message.isFromUser ? .trailing : .leading)
                                    .combined(with: .opacity),
                                removal: .opacity
                            ))
                            .animation(
                                .spring(response: 0.6, dampingFraction: 0.8)
                                .delay(Double(index) * 0.05),
                                value: conversation.messages.count
                            )
                        }
                    }

                    // Enhanced loading indicator
                    if isLoading {
                        HStack {
                            EnhancedTypingIndicator(agentColor: agent.agentType.color)
                            Spacer()
                        }
                        .padding(.horizontal)
                    }
                }
                .padding()
            }
            .onChange(of: conversation?.messages.count) { oldValue, newValue in
                if let lastMessage = conversation?.messages.last {
                    withAnimation(.easeOut(duration: 0.5)) {
                        proxy.scrollTo(lastMessage.id, anchor: .bottom)
                    }
                }
            }
        }
    }

    // MARK: - Enhanced Input Area
    private var enhancedInputArea: some View {
        VStack(spacing: 12) {
            // Quick actions
            if !selectedFiles.isEmpty {
                attachedFilesView
            }

            // Main input
            HStack(spacing: 12) {
                // Attachment button
                Button(action: { showingAttachments = true }) {
                    Image(systemName: "paperclip")
                        .font(.title3)
                        .foregroundColor(.blue)
                }

                // Text input
                HStack {
                    TextField("Type your message...", text: $messageText, axis: .vertical)
                        .textFieldStyle(PlainTextFieldStyle())
                        .lineLimit(1...4)

                    if !messageText.isEmpty {
                        Button("Send") {
                            sendMessage()
                        }
                        .font(.caption)
                        .fontWeight(.semibold)
                        .foregroundColor(.white)
                        .padding(.horizontal, 12)
                        .padding(.vertical, 6)
                        .background(.blue)
                        .cornerRadius(12)
                    }
                }
                .padding(12)
                .background(.ultraThinMaterial)
                .cornerRadius(20)

                // Voice button
                Button(action: toggleVoiceRecording) {
                    Image(systemName: isRecording ? "stop.fill" : "mic.fill")
                        .font(.title3)
                        .foregroundColor(isRecording ? .red : .blue)
                        .scaleEffect(isRecording ? 1.1 : 1.0)
                        .animation(.spring(response: 0.3, dampingFraction: 0.6), value: isRecording)
                }
                .disabled(isLoading)
            }
            .padding()
            .background(.ultraThinMaterial)
        }
    }

    // MARK: - Attached Files View
    private var attachedFilesView: some View {
        ScrollView(.horizontal, showsIndicators: false) {
            HStack(spacing: 8) {
                ForEach(selectedFiles, id: \.self) { file in
                    HStack(spacing: 6) {
                        Image(systemName: "doc.fill")
                            .font(.caption)
                            .foregroundColor(.blue)

                        Text(file.lastPathComponent)
                            .font(.caption)
                            .lineLimit(1)

                        Button(action: { removeFile(file) }) {
                            Image(systemName: "xmark.circle.fill")
                                .font(.caption)
                                .foregroundColor(.red)
                        }
                    }
                    .padding(.horizontal, 8)
                    .padding(.vertical, 4)
                    .background(.blue.opacity(0.1))
                    .cornerRadius(8)
                }
            }
            .padding(.horizontal)
        }
    }

    // MARK: - Audio Functions
    private func setupAudio() {
        #if targetEnvironment(simulator)
        // Skip audio setup in simulator to avoid errors
        print("Audio setup skipped in simulator")
        return
        #else
        let audioSession = AVAudioSession.sharedInstance()
        do {
            try audioSession.setCategory(.playAndRecord, mode: .default, options: [.defaultToSpeaker, .allowBluetooth])
            try audioSession.setActive(true, options: .notifyOthersOnDeactivation)
        } catch {
            print("Failed to setup audio session: \(error)")
            errorMessage = "Audio setup failed: \(error.localizedDescription)"
        }
        #endif
    }

    private func toggleVoiceRecording() {
        if isRecording {
            stopRecording()
        } else {
            startRecording()
        }
    }

    private func startRecording() {
        #if targetEnvironment(simulator)
        // Simulate recording in simulator
        isRecording = true
        messageText = "Voice recording simulated in simulator"

        // Simulate recording for 2 seconds
        DispatchQueue.main.asyncAfter(deadline: .now() + 2) {
            self.stopRecording()
        }
        return
        #else
        // Check microphone permission first
        AVAudioSession.sharedInstance().requestRecordPermission { granted in
            DispatchQueue.main.async {
                guard granted else {
                    self.errorMessage = "Microphone permission is required for voice interactions"
                    self.showingError = true
                    return
                }

                self.performRecording()
            }
        }
        #endif
    }

    private func performRecording() {
        let documentsPath = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask)[0]
        let audioFilename = documentsPath.appendingPathComponent("recording_\(Date().timeIntervalSince1970).m4a")
        recordingURL = audioFilename

        let settings = [
            AVFormatIDKey: Int(kAudioFormatMPEG4AAC),
            AVSampleRateKey: 44100,
            AVNumberOfChannelsKey: 1,
            AVEncoderAudioQualityKey: AVAudioQuality.high.rawValue
        ]

        do {
            audioRecorder = try AVAudioRecorder(url: audioFilename, settings: settings)
            audioRecorder?.prepareToRecord()
            audioRecorder?.record()
            isRecording = true

            // Haptic feedback
            let impactFeedback = UIImpactFeedbackGenerator(style: .medium)
            impactFeedback.impactOccurred()
        } catch {
            errorMessage = "Failed to start recording: \(error.localizedDescription)"
            showingError = true
        }
    }

    private func stopRecording() {
        audioRecorder?.stop()
        isRecording = false

        // Process the recorded audio
        if let recordingURL = recordingURL {
            processVoiceInput(audioURL: recordingURL)
        }

        // Haptic feedback
        let impactFeedback = UIImpactFeedbackGenerator(style: .light)
        impactFeedback.impactOccurred()
    }

    private func processVoiceInput(audioURL: URL) {
        // TODO: Implement speech-to-text and send to Gemini Live
        // For now, we'll simulate voice processing
        messageText = "Voice message processed..."
        sendMessage()
    }

    // MARK: - File Management
    private func removeFile(_ file: URL) {
        selectedFiles.removeAll { $0 == file }
    }

    // MARK: - Conversation Functions
    private func startConversation() {
        guard let student = mockAuthService.currentStudent else { return }

        Task {
            let newConversation = await conversationManager.startConversationWithAgent(
                student: student,
                agent: agent
            )

            await MainActor.run {
                self.conversation = newConversation
            }
        }
    }

    private func sendMessage() {
        guard !messageText.isEmpty,
              let conversation = conversation,
              let student = mockAuthService.currentStudent else { return }

        let message = messageText
        messageText = ""
        isLoading = true

        // Add haptic feedback
        let impactFeedback = UIImpactFeedbackGenerator(style: .light)
        impactFeedback.impactOccurred()

        Task {
            do {
                // Upload files to knowledge base if any
                if !selectedFiles.isEmpty {
                    await uploadFilesToKnowledgeBase()
                }

                let _ = try await conversationManager.sendMessageToAgent(
                    message,
                    to: conversation,
                    student: student,
                    agent: agent
                )

                // Save conversation to Supabase
                try await supabaseService.saveConversation(conversation)

                await MainActor.run {
                    self.isLoading = false
                    self.selectedFiles.removeAll()
                }
            } catch {
                await MainActor.run {
                    self.isLoading = false
                    self.errorMessage = error.localizedDescription
                    self.showingError = true
                }
            }
        }
    }

    private func uploadFilesToKnowledgeBase() async {
        // TODO: Implement file upload to Supabase knowledge base
        for file in selectedFiles {
            // Upload file and add to agent's knowledge base
            print("Uploading file: \(file.lastPathComponent)")
        }
    }

    private func clearConversation() {
        conversationManager.agentConversations.removeValue(forKey: agent.id)
        conversation = nil
        startConversation()
    }
}

// MARK: - Supporting Views

struct EnhancedMessageBubble: View {
    let message: ConversationMessage
    let agentName: String
    let agentType: AgentType

    var body: some View {
        HStack {
            if !message.isFromUser {
                // Agent message (left aligned)
                VStack(alignment: .leading, spacing: 8) {
                    HStack(spacing: 8) {
                        Image(systemName: agentType.icon)
                            .font(.caption)
                            .foregroundColor(agentType.color)

                        Text(agentName)
                            .font(.caption)
                            .fontWeight(.medium)
                            .foregroundColor(agentType.color)

                        Spacer()

                        Text(message.timestamp.formatted(date: .omitted, time: .shortened))
                            .font(.caption2)
                            .foregroundColor(.secondary)
                    }

                    Text(message.content)
                        .font(.body)
                        .padding(16)
                        .background(
                            LinearGradient(
                                colors: [agentType.color.opacity(0.1), agentType.color.opacity(0.05)],
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            )
                        )
                        .foregroundColor(.primary)
                        .cornerRadius(20, corners: [.topRight, .bottomLeft, .bottomRight])
                        .overlay(
                            RoundedRectangle(cornerRadius: 20)
                                .stroke(agentType.color.opacity(0.2), lineWidth: 1)
                        )
                }
                .frame(maxWidth: .infinity * 0.8, alignment: .leading)

                Spacer()
            } else {
                // User message (right aligned)
                Spacer()

                VStack(alignment: .trailing, spacing: 8) {
                    HStack {
                        Text(message.timestamp.formatted(date: .omitted, time: .shortened))
                            .font(.caption2)
                            .foregroundColor(.secondary)

                        Text("You")
                            .font(.caption)
                            .fontWeight(.medium)
                            .foregroundColor(.blue)

                        Image(systemName: "person.circle.fill")
                            .font(.caption)
                            .foregroundColor(.blue)
                    }

                    Text(message.content)
                        .font(.body)
                        .padding(16)
                        .background(
                            LinearGradient(
                                colors: [.blue, .blue.opacity(0.8)],
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            )
                        )
                        .foregroundColor(.white)
                        .cornerRadius(20, corners: [.topLeft, .bottomLeft, .bottomRight])
                        .shadow(color: .blue.opacity(0.3), radius: 4, x: 0, y: 2)
                }
                .frame(maxWidth: .infinity * 0.8, alignment: .trailing)
            }
        }
    }
}

struct EnhancedTypingIndicator: View {
    let agentColor: Color
    @State private var animationPhase = 0

    var body: some View {
        HStack(spacing: 4) {
            ForEach(0..<3) { index in
                Circle()
                    .fill(agentColor)
                    .frame(width: 8, height: 8)
                    .scaleEffect(animationPhase == index ? 1.2 : 0.8)
                    .animation(
                        .easeInOut(duration: 0.6)
                        .repeatForever(autoreverses: true)
                        .delay(Double(index) * 0.2),
                        value: animationPhase
                    )
            }
        }
        .padding(12)
        .background(agentColor.opacity(0.1))
        .cornerRadius(16)
        .onAppear {
            animationPhase = 0
        }
    }
}

// MARK: - Extensions
extension AgentType {
    var color: Color {
        switch self {
        case .subjectSpecialist: return .blue
        case .learningCoach: return .green
        case .emotionalSupport: return .pink
        case .assessmentAgent: return .orange
        case .parentCommunicator: return .purple
        case .adaptiveTutor: return .indigo
        case .creativeMentor: return .red
        case .socialSkillsCoach: return .teal
        }
    }

    var icon: String {
        switch self {
        case .subjectSpecialist: return "graduationcap.fill"
        case .learningCoach: return "person.crop.circle.badge.checkmark"
        case .emotionalSupport: return "heart.fill"
        case .assessmentAgent: return "chart.bar.fill"
        case .parentCommunicator: return "bubble.left.and.bubble.right.fill"
        case .adaptiveTutor: return "brain.head.profile"
        case .creativeMentor: return "paintbrush.fill"
        case .socialSkillsCoach: return "person.3.fill"
        }
    }
}

extension View {
    func cornerRadius(_ radius: CGFloat, corners: UIRectCorner) -> some View {
        clipShape(RoundedCorner(radius: radius, corners: corners))
    }
}

struct RoundedCorner: Shape {
    var radius: CGFloat = .infinity
    var corners: UIRectCorner = .allCorners

    func path(in rect: CGRect) -> Path {
        let path = UIBezierPath(
            roundedRect: rect,
            byRoundingCorners: corners,
            cornerRadii: CGSize(width: radius, height: radius)
        )
        return Path(path.cgPath)
    }
}
