//
//  KnowledgeBaseManagementView.swift
//  SpecialSparkAI
//
//  Created by MAGESH DHANASEKARAN on 5/23/25.
//

import SwiftUI

struct KnowledgeBaseManagementView: View {
    @StateObject private var supabaseService = SupabaseService.shared
    @StateObject private var aiAgentService = AIAgentService()
    @State private var selectedSubject: String = "All"
    @State private var knowledgeBaseFiles: [KnowledgeBaseFile] = []
    @State private var isLoading = false
    @State private var showingFilePicker = false
    @State private var selectedFiles: [URL] = []
    @State private var searchText = ""

    private let subjects = ["All", "Mathematics", "Reading & Language", "Science", "Art", "Social Studies", "Music", "Physical Education"]

    var filteredFiles: [KnowledgeBaseFile] {
        var files = knowledgeBaseFiles

        if selectedSubject != "All" {
            files = files.filter { file in
                // Filter by subject based on agent specialization
                return true // TODO: Implement proper filtering
            }
        }

        if !searchText.isEmpty {
            files = files.filter { file in
                file.fileName.localizedCaseInsensitiveContains(searchText)
            }
        }

        return files
    }

    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // Header with stats
                knowledgeBaseHeader

                // Subject filter
                subjectFilterView

                // Search bar
                searchBarView

                // Files list
                if filteredFiles.isEmpty && !isLoading {
                    emptyStateView
                } else {
                    filesListView
                }

                Spacer()
            }
            .navigationTitle("Knowledge Base")
            .navigationBarTitleDisplayMode(.large)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Add Files") {
                        showingFilePicker = true
                    }
                }
            }
            .onAppear {
                loadKnowledgeBase()
            }
            .sheet(isPresented: $showingFilePicker) {
                DocumentPicker(selectedFiles: $selectedFiles)
            }
            .onChange(of: selectedFiles) { oldValue, newValue in
                if !newValue.isEmpty {
                    uploadFiles()
                }
            }
        }
    }

    private var knowledgeBaseHeader: some View {
        VStack(spacing: 16) {
            HStack(spacing: 20) {
                // Total files
                StatCard(
                    title: "Total Files",
                    value: "\(knowledgeBaseFiles.count)",
                    icon: "doc.fill",
                    color: .blue
                )

                // Processed files
                StatCard(
                    title: "Processed",
                    value: "\(knowledgeBaseFiles.filter { $0.isProcessed }.count)",
                    icon: "checkmark.circle.fill",
                    color: .green
                )

                // Total size
                StatCard(
                    title: "Total Size",
                    value: formatTotalSize(),
                    icon: "externaldrive.fill",
                    color: .orange
                )
            }
            .padding()
            .background(.ultraThinMaterial)
        }
    }

    private var subjectFilterView: some View {
        ScrollView(.horizontal, showsIndicators: false) {
            HStack(spacing: 12) {
                ForEach(subjects, id: \.self) { subject in
                    Button(action: { selectedSubject = subject }) {
                        Text(subject)
                            .font(.caption)
                            .fontWeight(.medium)
                            .padding(.horizontal, 16)
                            .padding(.vertical, 8)
                            .background(
                                selectedSubject == subject ? .blue : .gray.opacity(0.2)
                            )
                            .foregroundColor(
                                selectedSubject == subject ? .white : .primary
                            )
                            .cornerRadius(20)
                    }
                    .animation(.spring(response: 0.3, dampingFraction: 0.7), value: selectedSubject)
                }
            }
            .padding(.horizontal)
        }
        .padding(.vertical, 8)
    }

    private var searchBarView: some View {
        HStack {
            Image(systemName: "magnifyingglass")
                .foregroundColor(.secondary)

            TextField("Search files...", text: $searchText)
                .textFieldStyle(PlainTextFieldStyle())

            if !searchText.isEmpty {
                Button("Clear") {
                    searchText = ""
                }
                .font(.caption)
                .foregroundColor(.blue)
            }
        }
        .padding()
        .background(.ultraThinMaterial)
        .cornerRadius(12)
        .padding(.horizontal)
    }

    private var emptyStateView: some View {
        VStack(spacing: 20) {
            Image(systemName: "brain.head.profile")
                .font(.system(size: 60))
                .foregroundColor(.secondary)

            VStack(spacing: 8) {
                Text("No Knowledge Base Files")
                    .font(.title2)
                    .fontWeight(.semibold)

                Text("Upload documents, images, and other files to enhance your AI teachers' knowledge and capabilities.")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
                    .padding(.horizontal)
            }

            Button("Add Your First Files") {
                showingFilePicker = true
            }
            .font(.headline)
            .foregroundColor(.white)
            .padding()
            .background(.blue)
            .cornerRadius(12)
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
    }

    private var filesListView: some View {
        ScrollView {
            LazyVStack(spacing: 12) {
                ForEach(filteredFiles) { file in
                    KnowledgeBaseFileRow(
                        file: file,
                        agentColor: .blue
                    ) {
                        deleteFile(file)
                    }
                }
            }
            .padding()
        }
    }

    private func loadKnowledgeBase() {
        isLoading = true

        Task {
            // TODO: Load from Supabase
            // Simulate loading with sample data
            try? await Task.sleep(nanoseconds: 1_000_000_000)

            await MainActor.run {
                self.knowledgeBaseFiles = [
                    KnowledgeBaseFile(
                        id: UUID(),
                        agentId: UUID(),
                        fileName: "Math Curriculum Guide.pdf",
                        fileType: "pdf",
                        fileSize: 2_500_000,
                        uploadDate: Date().addingTimeInterval(-86400),
                        isProcessed: true
                    ),
                    KnowledgeBaseFile(
                        id: UUID(),
                        agentId: UUID(),
                        fileName: "Reading Comprehension Strategies.docx",
                        fileType: "docx",
                        fileSize: 1_200_000,
                        uploadDate: Date().addingTimeInterval(-3600),
                        isProcessed: false
                    )
                ]
                self.isLoading = false
            }
        }
    }

    private func uploadFiles() {
        guard !selectedFiles.isEmpty else { return }

        isLoading = true

        Task {
            for fileURL in selectedFiles {
                let file = KnowledgeBaseFile(
                    id: UUID(),
                    agentId: UUID(), // TODO: Associate with specific agent
                    fileName: fileURL.lastPathComponent,
                    fileType: fileURL.pathExtension,
                    fileSize: getFileSize(url: fileURL),
                    uploadDate: Date(),
                    isProcessed: false
                )

                await MainActor.run {
                    self.knowledgeBaseFiles.append(file)
                }
            }

            await MainActor.run {
                self.selectedFiles.removeAll()
                self.isLoading = false
            }
        }
    }

    private func deleteFile(_ file: KnowledgeBaseFile) {
        knowledgeBaseFiles.removeAll { $0.id == file.id }
        // TODO: Delete from Supabase
    }

    private func getFileSize(url: URL) -> Int64 {
        do {
            let resources = try url.resourceValues(forKeys: [.fileSizeKey])
            return Int64(resources.fileSize ?? 0)
        } catch {
            return 0
        }
    }

    private func formatTotalSize() -> String {
        let totalBytes = knowledgeBaseFiles.reduce(0) { $0 + $1.fileSize }
        let formatter = ByteCountFormatter()
        formatter.allowedUnits = [.useKB, .useMB, .useGB]
        formatter.countStyle = .file
        return formatter.string(fromByteCount: totalBytes)
    }
}


