//
//  AITeachersView.swift
//  SpecialSparkAI
//
//  AI Agent Teachers with LangGraph and CrewAI Integration
//

import SwiftUI
import SwiftData

struct AITeachersView: View {
    @StateObject private var mockAuthService = MockAuthService.shared
    @StateObject private var aiAgentService = AIAgentService()
    @StateObject private var conversationManager = ConversationManager.shared
    @StateObject private var settingsManager = SettingsManager.shared
    @State private var selectedAgent: AIAgent?
    @State private var showingAgentDetail = false
    @State private var showingChat = false
    @State private var searchText = ""
    @State private var selectedAgentType: AgentType?
    @State private var showingStudentProfile = false
    @State private var showingCrewBuilder = false
    @State private var gradeBasedTeachers: [AIAgent] = []
    @State private var selectedSubjectCategory: SubjectCategory?

    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // Student Grade Header
                studentGradeHeader

                // Connection Status
                connectionStatusBar

                // Subject Category Filter
                subjectCategoryFilter

                // Search Bar
                searchBar

                // Main Content
                if !mockAuthService.isAuthenticated {
                    authenticationPrompt
                } else if gradeBasedTeachers.isEmpty {
                    loadingTeachersState
                } else {
                    teachersGridView
                }

                Spacer()

                // Action Buttons
                actionButtonsBar
            }
            .navigationTitle("Your AI Teachers")
            .navigationBarTitleDisplayMode(.large)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Menu {
                        Button("Student Profile", systemImage: "person.circle") {
                            showingStudentProfile = true
                        }

                        Button("Build Study Crew", systemImage: "person.3.fill") {
                            showingCrewBuilder = true
                        }

                        Button("Learning Analytics", systemImage: "chart.bar.fill") {
                            // Show analytics
                        }
                    } label: {
                        Image(systemName: "ellipsis.circle")
                            .font(.title2)
                    }
                }
            }
            .onAppear {
                loadGradeBasedTeachers()
            }
            .onChange(of: mockAuthService.currentStudent) { _, _ in
                loadGradeBasedTeachers()
            }
            .onChange(of: settingsManager.gradeChangeNotification) { _, newValue in
                if newValue {
                    loadGradeBasedTeachers()
                }
            }
        }
        .sheet(isPresented: $showingStudentProfile) {
            StudentProfileView()
        }
        .sheet(isPresented: $showingCrewBuilder) {
            CrewBuilderView(
                aiAgentService: aiAgentService,
                availableAgents: gradeBasedTeachers
            )
        }
        .sheet(isPresented: $showingChat) {
            if let agent = selectedAgent {
                AgentChatView(agent: agent)
            }
        }
        .sheet(isPresented: $showingAgentDetail) {
            if let agent = selectedAgent {
                AgentDetailView(agent: agent, aiAgentService: aiAgentService)
            }
        }
    }

    // MARK: - UI Components

    private var studentGradeHeader: some View {
        VStack(spacing: 12) {
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    if let student = mockAuthService.currentStudent {
                        Text("Hello, \(student.firstName)!")
                            .font(.largeTitle)
                            .fontWeight(.bold)
                            .foregroundStyle(
                                LinearGradient(
                                    colors: [.blue, .purple, .pink],
                                    startPoint: .leading,
                                    endPoint: .trailing
                                )
                            )

                        Text("\(student.gradeLevel.displayName) • \(student.schoolLevel.displayName)")
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                    } else {
                        Text("Your AI Teachers")
                            .font(.largeTitle)
                            .fontWeight(.bold)
                            .foregroundStyle(
                                LinearGradient(
                                    colors: [.blue, .purple, .pink],
                                    startPoint: .leading,
                                    endPoint: .trailing
                                )
                            )

                        Text("Sign in to see your grade-specific teachers")
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                    }
                }

                Spacer()

                // Teacher Stats
                VStack(alignment: .trailing, spacing: 2) {
                    Text("\(gradeBasedTeachers.count)")
                        .font(.title)
                        .fontWeight(.bold)
                        .foregroundColor(.blue)

                    Text("AI Teachers")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }

            // Special Needs Indicator
            if let student = mockAuthService.currentStudent,
               !student.specialNeeds.isEmpty && student.specialNeeds != [.none] {
                HStack {
                    Image(systemName: "heart.fill")
                        .foregroundColor(.pink)

                    Text("Specialized support for: \(student.specialNeeds.map { $0.displayName }.joined(separator: ", "))")
                        .font(.caption)
                        .foregroundColor(.secondary)

                    Spacer()
                }
                .padding(.horizontal)
                .padding(.vertical, 8)
                .background(Color.pink.opacity(0.1))
                .cornerRadius(8)
            }
        }
        .padding()
        .background(
            LinearGradient(
                colors: [Color.blue.opacity(0.05), Color.purple.opacity(0.05)],
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )
        )
    }

    private var connectionStatusBar: some View {
        HStack {
            Circle()
                .fill(connectionStatusColor)
                .frame(width: 8, height: 8)

            Text(connectionStatusText)
                .font(.caption)
                .foregroundColor(.secondary)

            Spacer()

            if case .connected = aiAgentService.connectionStatus {
                HStack(spacing: 4) {
                    Image(systemName: "brain.head.profile")
                        .foregroundColor(.green)
                    Text("Gemini Flash 2.0")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }
        }
        .padding(.horizontal)
        .padding(.vertical, 8)
        .background(Color(.systemGray6))
    }

    private var connectionStatusColor: Color {
        switch aiAgentService.connectionStatus {
        case .connected:
            return .green
        case .connecting:
            return .orange
        case .disconnected:
            return .red
        case .error:
            return .red
        }
    }

    private var connectionStatusText: String {
        switch aiAgentService.connectionStatus {
        case .connected:
            return "Connected to AI Services"
        case .connecting:
            return "Connecting to AI Services..."
        case .disconnected:
            return "Disconnected from AI Services"
        case .error(let message):
            return "Error: \(message)"
        }
    }

    private var subjectCategoryFilter: some View {
        ScrollView(.horizontal, showsIndicators: false) {
            HStack(spacing: 12) {
                SubjectCategoryFilterChip(
                    title: "All Subjects",
                    isSelected: selectedSubjectCategory == nil,
                    icon: "sparkles"
                ) {
                    selectedSubjectCategory = nil
                }

                ForEach(SubjectCategory.allCases, id: \.self) { category in
                    SubjectCategoryFilterChip(
                        title: category.displayName,
                        isSelected: selectedSubjectCategory == category,
                        icon: iconForSubjectCategory(category)
                    ) {
                        selectedSubjectCategory = category
                    }
                }
            }
            .padding(.horizontal)
        }
        .padding(.vertical, 8)
    }

    private var searchBar: some View {
        HStack {
            Image(systemName: "magnifyingglass")
                .foregroundColor(.secondary)

            TextField("Search AI agents...", text: $searchText)
                .textFieldStyle(PlainTextFieldStyle())

            if !searchText.isEmpty {
                Button("Clear") {
                    searchText = ""
                }
                .font(.caption)
                .foregroundColor(.blue)
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(.ultraThinMaterial)
        )
        .padding(.horizontal)
    }

    private var authenticationPrompt: some View {
        VStack(spacing: 20) {
            Spacer()

            Image(systemName: "person.circle")
                .font(.system(size: 80))
                .foregroundColor(.blue.opacity(0.3))

            VStack(spacing: 8) {
                Text("Welcome to SpecialSpark AI")
                    .font(.title2)
                    .fontWeight(.semibold)

                Text("Sign in to see your personalized AI teachers based on your grade level and learning needs")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
            }

            Button("Sign In") {
                // Navigate to auth view
            }
            .buttonStyle(.borderedProminent)
            .controlSize(.large)

            Spacer()
        }
        .padding()
    }

    private var loadingTeachersState: some View {
        VStack(spacing: 20) {
            Spacer()

            ProgressView()
                .scaleEffect(1.5)

            VStack(spacing: 8) {
                Text("Loading Your AI Teachers")
                    .font(.title2)
                    .fontWeight(.semibold)

                Text("Preparing personalized teachers for your grade level...")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
            }

            Spacer()
        }
        .padding()
    }

    private var teachersGridView: some View {
        ScrollView {
            LazyVGrid(columns: [
                GridItem(.flexible()),
                GridItem(.flexible())
            ], spacing: 16) {
                ForEach(filteredTeachers) { teacher in
                    AITeacherCard(teacher: teacher) {
                        selectedAgent = teacher
                        showingChat = true
                    }
                }
            }
            .padding()
        }
    }

    private var actionButtonsBar: some View {
        HStack(spacing: 16) {
            Button("Quick Chat") {
                // Start quick chat with available teacher
                if let teacher = gradeBasedTeachers.first {
                    selectedAgent = teacher
                    showingChat = true
                }
            }
            .buttonStyle(.bordered)
            .disabled(gradeBasedTeachers.isEmpty)

            Button("Study Crew") {
                showingCrewBuilder = true
            }
            .buttonStyle(.bordered)
            .disabled(gradeBasedTeachers.count < 2)

            Button("Profile") {
                showingStudentProfile = true
            }
            .buttonStyle(.borderedProminent)
        }
        .padding()
        .background(.ultraThinMaterial)
    }

    // MARK: - Helper Methods

    private func loadGradeBasedTeachers() {
        guard let student = mockAuthService.currentStudent else {
            gradeBasedTeachers = []
            return
        }

        // Load teachers for the student's grade level
        gradeBasedTeachers = GradeBasedTeacherService.shared.getTeachersFor(student: student)
    }

    private var filteredTeachers: [AIAgent] {
        var filtered = gradeBasedTeachers

        // Filter by agent type
        if let selectedType = selectedAgentType {
            filtered = filtered.filter { $0.agentType == selectedType }
        }

        // Filter by subject category
        if let selectedCategory = selectedSubjectCategory {
            let subjects = SubjectDataService.shared.allSubjects.filter { $0.category == selectedCategory }
            let subjectIds = Set(subjects.map { $0.id })
            filtered = filtered.filter { teacher in
                if let subjectId = teacher.subjectId {
                    return subjectIds.contains(subjectId)
                }
                return selectedCategory == .specialNeeds && teacher.agentType != .subjectSpecialist
            }
        }

        // Filter by search text
        if !searchText.isEmpty {
            filtered = filtered.filter { teacher in
                teacher.name.localizedCaseInsensitiveContains(searchText) ||
                teacher.specialization.localizedCaseInsensitiveContains(searchText) ||
                teacher.agentType.rawValue.localizedCaseInsensitiveContains(searchText)
            }
        }

        return filtered
    }

    private func iconForAgentType(_ agentType: AgentType) -> String {
        switch agentType {
        case .subjectSpecialist:
            return "graduationcap.fill"
        case .learningCoach:
            return "person.crop.circle.badge.checkmark"
        case .emotionalSupport:
            return "heart.fill"
        case .assessmentAgent:
            return "chart.bar.fill"
        case .parentCommunicator:
            return "bubble.left.and.bubble.right.fill"
        case .adaptiveTutor:
            return "brain.head.profile"
        case .creativeMentor:
            return "paintbrush.fill"
        case .socialSkillsCoach:
            return "person.3.fill"
        }
    }

    private func iconForSubjectCategory(_ category: SubjectCategory) -> String {
        switch category {
        case .core:
            return "book.fill"
        case .language:
            return "globe"
        case .arts:
            return "paintbrush.fill"
        case .stem:
            return "atom"
        case .socialStudies:
            return "building.columns.fill"
        case .physicalEducation:
            return "figure.run"
        case .specialNeeds:
            return "heart.fill"
        case .ap:
            return "star.fill"
        case .elective:
            return "sparkles"
        case .lifeSkills:
            return "house.fill"
        case .careerTech:
            return "hammer.fill"
        case .health:
            return "cross.fill"
        case .technology:
            return "laptopcomputer"
        case .business:
            return "briefcase.fill"
        case .dualEnrollment:
            return "graduationcap.fill"
        case .collegePrep:
            return "book.closed.fill"
        }
    }
}

// MARK: - Supporting Views

struct SubjectCategoryFilterChip: View {
    let title: String
    let isSelected: Bool
    let icon: String
    let action: () -> Void

    var body: some View {
        Button(action: action) {
            HStack(spacing: 6) {
                Image(systemName: icon)
                    .font(.caption)

                Text(title)
                    .font(.caption)
                    .fontWeight(.medium)
            }
            .padding(.horizontal, 12)
            .padding(.vertical, 8)
            .background(
                RoundedRectangle(cornerRadius: 20)
                    .fill(isSelected ? .blue : .clear)
                    .overlay(
                        RoundedRectangle(cornerRadius: 20)
                            .stroke(.blue, lineWidth: 1)
                    )
            )
            .foregroundColor(isSelected ? .white : .blue)
        }
        .buttonStyle(PlainButtonStyle())
    }
}

struct AITeacherCard: View {
    let teacher: AIAgent
    let action: () -> Void
    @StateObject private var conversationManager = ConversationManager.shared

    var body: some View {
        VStack(spacing: 0) {
            // Main card content
            Button(action: action) {
            VStack(spacing: 12) {
                // Teacher Avatar
                ZStack {
                    Circle()
                        .fill(
                            LinearGradient(
                                colors: agentTypeGradient(for: teacher.agentType),
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            )
                        )
                        .frame(width: 60, height: 60)

                    Image(systemName: agentTypeIcon(for: teacher.agentType))
                        .font(.title2)
                        .foregroundColor(.white)

                    // Status indicator
                    if teacher.isActive {
                        Circle()
                            .fill(.green)
                            .frame(width: 16, height: 16)
                            .overlay(
                                Circle()
                                    .stroke(.white, lineWidth: 2)
                            )
                            .offset(x: 20, y: 20)
                    }
                }

                // Teacher Info
                VStack(spacing: 4) {
                    Text(teacher.name)
                        .font(.subheadline)
                        .fontWeight(.semibold)
                        .lineLimit(1)

                    Text(teacher.specialization)
                        .font(.caption)
                        .foregroundColor(.blue)
                        .lineLimit(1)

                    Text(teacher.gradeRange)
                        .font(.caption2)
                        .foregroundColor(.secondary)
                        .lineLimit(1)
                }

                // Special Needs Support Indicator
                if !teacher.specialNeedsAdaptations.isEmpty {
                    HStack(spacing: 4) {
                        Image(systemName: "heart.fill")
                            .font(.caption2)
                            .foregroundColor(.pink)

                        Text("Special Support")
                            .font(.caption2)
                            .foregroundColor(.secondary)
                    }
                }

                // Conversation History Indicator
                if conversationManager.hasConversationWithAgent(teacher.id) {
                    HStack(spacing: 4) {
                        Image(systemName: "bubble.left.and.bubble.right.fill")
                            .font(.caption2)
                            .foregroundColor(.green)

                        Text("Chat History")
                            .font(.caption2)
                            .foregroundColor(.secondary)
                    }
                }
            }
            .padding()
            .background(
                RoundedRectangle(cornerRadius: 16)
                    .fill(.ultraThinMaterial)
                    .overlay(
                        RoundedRectangle(cornerRadius: 16)
                            .stroke(.blue.opacity(0.2), lineWidth: 1)
                    )
            )
            }
            .buttonStyle(PlainButtonStyle())

            // Chat Button
            Button("💬 Chat") {
                // This will be handled by the parent view
                action()
            }
            .font(.caption)
            .fontWeight(.medium)
            .foregroundColor(.white)
            .frame(maxWidth: .infinity)
            .padding(.vertical, 8)
            .background(
                LinearGradient(
                    colors: agentTypeGradient(for: teacher.agentType),
                    startPoint: .leading,
                    endPoint: .trailing
                )
            )
            .clipShape(
                RoundedRectangle(cornerRadius: 12)
            )
        }
    }

    private func agentTypeGradient(for agentType: AgentType) -> [Color] {
        switch agentType {
        case .subjectSpecialist:
            return [.blue, .cyan]
        case .learningCoach:
            return [.green, .mint]
        case .emotionalSupport:
            return [.pink, .red]
        case .assessmentAgent:
            return [.purple, .blue]
        case .parentCommunicator:
            return [.orange, .yellow]
        case .adaptiveTutor:
            return [.teal, .blue]
        case .creativeMentor:
            return [.pink, .purple]
        case .socialSkillsCoach:
            return [.indigo, .purple]
        }
    }

    private func agentTypeIcon(for agentType: AgentType) -> String {
        switch agentType {
        case .subjectSpecialist:
            return "graduationcap.fill"
        case .learningCoach:
            return "person.crop.circle.badge.checkmark"
        case .emotionalSupport:
            return "heart.fill"
        case .assessmentAgent:
            return "chart.bar.fill"
        case .parentCommunicator:
            return "bubble.left.and.bubble.right.fill"
        case .adaptiveTutor:
            return "brain.head.profile"
        case .creativeMentor:
            return "paintbrush.fill"
        case .socialSkillsCoach:
            return "person.3.fill"
        }
    }

    private func stateColor(for state: AgentState) -> Color {
        switch state {
        case .idle:
            return .gray
        case .teaching:
            return .green
        case .assessing:
            return .blue
        case .planning:
            return .orange
        case .collaborating:
            return .purple
        case .reflecting:
            return .yellow
        case .adapting:
            return .teal
        case .communicating:
            return .pink
        }
    }
}

// MARK: - Placeholder Views (to be implemented)

struct CreateAgentView: View {
    let aiAgentService: AIAgentService

    var body: some View {
        Text("Create Agent View - To be implemented")
    }
}

struct CrewBuilderView: View {
    let aiAgentService: AIAgentService
    let availableAgents: [AIAgent]

    var body: some View {
        Text("Crew Builder View - To be implemented")
    }
}

struct AgentDetailView: View {
    let agent: AIAgent
    let aiAgentService: AIAgentService

    var body: some View {
        Text("Agent Detail View - To be implemented")
    }
}

struct AgentChatView: View {
    let agent: AIAgent
    @StateObject private var conversationManager = ConversationManager.shared
    @StateObject private var mockAuthService = MockAuthService.shared
    @State private var messageText = ""
    @State private var conversation: Conversation?
    @State private var isLoading = false
    @State private var showingError = false
    @State private var errorMessage = ""
    @Environment(\.dismiss) private var dismiss

    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // Agent Header
                agentHeader

                // Messages List
                messagesScrollView

                // Message Input
                messageInputView
            }
            .navigationTitle("Chat with \(agent.name)")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Done") {
                        dismiss()
                    }
                }

                ToolbarItem(placement: .navigationBarTrailing) {
                    Menu {
                        Button("Clear Chat", systemImage: "trash") {
                            clearConversation()
                        }
                        .foregroundColor(.red)

                        Button("Agent Info", systemImage: "info.circle") {
                            // Show agent details
                        }
                    } label: {
                        Image(systemName: "ellipsis.circle")
                    }
                }
            }
            .onAppear {
                startConversation()
            }
            .alert("Error", isPresented: $showingError) {
                Button("OK") { }
            } message: {
                Text(errorMessage)
            }
        }
    }

    // MARK: - UI Components

    private var agentHeader: some View {
        HStack(spacing: 12) {
            // Agent Avatar
            ZStack {
                Circle()
                    .fill(
                        LinearGradient(
                            colors: agentTypeGradient(for: agent.agentType),
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    )
                    .frame(width: 50, height: 50)

                Image(systemName: agentTypeIcon(for: agent.agentType))
                    .font(.title3)
                    .foregroundColor(.white)

                // Online indicator
                Circle()
                    .fill(.green)
                    .frame(width: 12, height: 12)
                    .overlay(
                        Circle()
                            .stroke(.white, lineWidth: 2)
                    )
                    .offset(x: 18, y: 18)
            }

            // Agent Info
            VStack(alignment: .leading, spacing: 2) {
                Text(agent.name)
                    .font(.headline)
                    .fontWeight(.semibold)

                Text(agent.specialization)
                    .font(.subheadline)
                    .foregroundColor(.blue)

                HStack(spacing: 4) {
                    Image(systemName: "brain.head.profile")
                        .font(.caption2)
                        .foregroundColor(.green)

                    Text("Powered by Gemini Flash 2.0")
                        .font(.caption2)
                        .foregroundColor(.secondary)
                }
            }

            Spacer()

            // Message count
            if let conversation = conversation {
                VStack(alignment: .trailing, spacing: 2) {
                    Text("\(conversation.messages.count)")
                        .font(.title3)
                        .fontWeight(.bold)
                        .foregroundColor(.blue)

                    Text("messages")
                        .font(.caption2)
                        .foregroundColor(.secondary)
                }
            }
        }
        .padding()
        .background(.ultraThinMaterial)
    }

    private var messagesScrollView: some View {
        ScrollViewReader { proxy in
            ScrollView {
                LazyVStack(spacing: 12) {
                    if let conversation = conversation {
                        ForEach(conversation.messages) { message in
                            MessageBubbleView(
                                message: message,
                                agentName: agent.name,
                                agentType: agent.agentType
                            )
                            .id(message.id)
                        }
                    }

                    // Loading indicator
                    if isLoading {
                        HStack {
                            TypingIndicatorView()
                            Spacer()
                        }
                        .padding(.horizontal)
                    }
                }
                .padding()
            }
            .onChange(of: conversation?.messages.count) { oldValue, newValue in
                // Auto-scroll to bottom when new message arrives
                if let lastMessage = conversation?.messages.last {
                    withAnimation(.easeOut(duration: 0.3)) {
                        proxy.scrollTo(lastMessage.id, anchor: .bottom)
                    }
                }
            }
        }
    }

    private var messageInputView: some View {
        VStack(spacing: 8) {
            // Quick response buttons
            if !messageText.isEmpty {
                quickResponseButtons
            }

            // Text input
            HStack(spacing: 12) {
                TextField("Type your message...", text: $messageText, axis: .vertical)
                    .textFieldStyle(RoundedBorderTextFieldStyle())
                    .lineLimit(1...4)

                Button(action: sendMessage) {
                    Image(systemName: "arrow.up.circle.fill")
                        .font(.title2)
                        .foregroundColor(messageText.isEmpty ? .gray : .blue)
                }
                .disabled(messageText.isEmpty || isLoading)
            }
        }
        .padding()
        .background(.ultraThinMaterial)
    }

    private var quickResponseButtons: some View {
        ScrollView(.horizontal, showsIndicators: false) {
            HStack(spacing: 8) {
                ForEach(getQuickResponses(), id: \.self) { response in
                    Button(response) {
                        messageText = response
                        sendMessage()
                    }
                    .font(.caption)
                    .padding(.horizontal, 12)
                    .padding(.vertical, 6)
                    .background(.blue.opacity(0.1))
                    .foregroundColor(.blue)
                    .cornerRadius(16)
                }
            }
            .padding(.horizontal)
        }
    }

    // MARK: - Actions

    private func startConversation() {
        guard let student = mockAuthService.currentStudent else { return }

        Task {
            let newConversation = await conversationManager.startConversationWithAgent(
                student: student,
                agent: agent
            )

            await MainActor.run {
                self.conversation = newConversation
            }
        }
    }

    private func sendMessage() {
        guard !messageText.isEmpty,
              let conversation = conversation,
              let student = mockAuthService.currentStudent else { return }

        let message = messageText
        messageText = ""

        Task {
            do {
                let _ = try await conversationManager.sendMessageToAgent(
                    message,
                    to: conversation,
                    student: student,
                    agent: agent
                )
            } catch {
                await MainActor.run {
                    self.errorMessage = error.localizedDescription
                    self.showingError = true
                }
            }
        }
    }

    private func clearConversation() {
        conversationManager.agentConversations.removeValue(forKey: agent.id)
        conversation = nil
        startConversation()
    }

    private func getQuickResponses() -> [String] {
        switch agent.agentType {
        case .subjectSpecialist:
            return ["Can you help me?", "I don't understand", "Can you explain more?", "What's next?"]
        case .emotionalSupport:
            return ["I'm feeling good", "I need encouragement", "I'm frustrated", "Thank you"]
        case .learningCoach:
            return ["How am I doing?", "What should I study?", "I need help", "Show me examples"]
        default:
            return ["Hi!", "Can you help?", "Thank you", "Tell me more"]
        }
    }

    // MARK: - Helper Methods

    private func agentTypeGradient(for agentType: AgentType) -> [Color] {
        switch agentType {
        case .subjectSpecialist: return [.blue, .cyan]
        case .learningCoach: return [.green, .mint]
        case .emotionalSupport: return [.pink, .red]
        case .assessmentAgent: return [.purple, .blue]
        case .parentCommunicator: return [.orange, .yellow]
        case .adaptiveTutor: return [.teal, .blue]
        case .creativeMentor: return [.pink, .purple]
        case .socialSkillsCoach: return [.indigo, .purple]
        }
    }

    private func agentTypeIcon(for agentType: AgentType) -> String {
        switch agentType {
        case .subjectSpecialist: return "graduationcap.fill"
        case .learningCoach: return "person.crop.circle.badge.checkmark"
        case .emotionalSupport: return "heart.fill"
        case .assessmentAgent: return "chart.bar.fill"
        case .parentCommunicator: return "bubble.left.and.bubble.right.fill"
        case .adaptiveTutor: return "brain.head.profile"
        case .creativeMentor: return "paintbrush.fill"
        case .socialSkillsCoach: return "person.3.fill"
        }
    }
}

// MARK: - Supporting Chat Views

struct MessageBubbleView: View {
    let message: ConversationMessage
    let agentName: String
    let agentType: AgentType

    var body: some View {
        HStack {
            if !message.isFromUser {
                // Agent message (left aligned)
                VStack(alignment: .leading, spacing: 4) {
                    HStack {
                        Image(systemName: agentTypeIcon(for: agentType))
                            .font(.caption)
                            .foregroundColor(.blue)

                        Text(agentName)
                            .font(.caption)
                            .fontWeight(.medium)
                            .foregroundColor(.blue)

                        Spacer()
                    }

                    Text(message.content)
                        .font(.body)
                        .padding(12)
                        .background(.blue.opacity(0.1))
                        .foregroundColor(.primary)
                        .cornerRadius(16, corners: [.topRight, .bottomLeft, .bottomRight])
                }
                .frame(maxWidth: .infinity * 0.8, alignment: .leading)

                Spacer()
            } else {
                // Student message (right aligned)
                Spacer()

                VStack(alignment: .trailing, spacing: 4) {
                    Text(message.content)
                        .font(.body)
                        .padding(12)
                        .background(.blue)
                        .foregroundColor(.white)
                        .cornerRadius(16, corners: [.topLeft, .bottomLeft, .bottomRight])

                    Text(formatTime(message.timestamp))
                        .font(.caption2)
                        .foregroundColor(.secondary)
                }
                .frame(maxWidth: .infinity * 0.8, alignment: .trailing)
            }
        }
    }

    private func formatTime(_ date: Date) -> String {
        let formatter = DateFormatter()
        formatter.timeStyle = .short
        return formatter.string(from: date)
    }

    private func agentTypeIcon(for agentType: AgentType) -> String {
        switch agentType {
        case .subjectSpecialist: return "graduationcap.fill"
        case .learningCoach: return "person.crop.circle.badge.checkmark"
        case .emotionalSupport: return "heart.fill"
        case .assessmentAgent: return "chart.bar.fill"
        case .parentCommunicator: return "bubble.left.and.bubble.right.fill"
        case .adaptiveTutor: return "brain.head.profile"
        case .creativeMentor: return "paintbrush.fill"
        case .socialSkillsCoach: return "person.3.fill"
        }
    }
}



struct TypingIndicatorView: View {
    @State private var animating = false

    var body: some View {
        HStack(spacing: 4) {
            ForEach(0..<3) { index in
                Circle()
                    .fill(.blue.opacity(0.6))
                    .frame(width: 8, height: 8)
                    .scaleEffect(animating ? 1.0 : 0.5)
                    .animation(
                        .easeInOut(duration: 0.6)
                        .repeatForever()
                        .delay(Double(index) * 0.2),
                        value: animating
                    )
            }

            Text("AI is thinking...")
                .font(.caption)
                .foregroundColor(.secondary)
        }
        .padding(12)
        .background(.gray.opacity(0.1))
        .cornerRadius(16, corners: [.topRight, .bottomLeft, .bottomRight])
        .onAppear {
            animating = true
        }
    }
}
