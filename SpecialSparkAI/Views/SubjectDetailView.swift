//
//  SubjectDetailView.swift
//  SpecialSparkAI
//
//  Created by MAGESH DHANASEKARAN on 5/23/25.
//

import SwiftUI

struct SubjectDetailView: View {
    let subject: Subject
    let gradeLevel: GradeLevel
    @StateObject private var lessonDataService = LessonDataService()
    @StateObject private var subjectDataService = SubjectDataService()
    @State private var selectedLesson: Lesson?
    @State private var showingLessonDetail = false
    @Environment(\.dismiss) private var dismiss

    private var filteredSubjects: [Subject] {
        return subjectDataService.getSubjectsForGrade(gradeLevel)
    }

    private var lessonsForSubject: [Lesson] {
        return lessonDataService.getLessonsForSubject(subject.id, gradeLevel: gradeLevel)
    }

    var body: some View {
        NavigationView {
            ScrollView(.vertical, showsIndicators: true) {
                VStack(spacing: 20) {
                    // Header
                    subjectHeaderView

                    // All Subjects for Grade
                    allSubjectsView

                    // Lessons for Selected Subject
                    if !lessonsForSubject.isEmpty {
                        lessonsView
                    }
                }
                .padding()
            }
            .navigationTitle("Academic Center")
            .navigationBarTitleDisplayMode(.large)

        }
        .sheet(isPresented: $showingLessonDetail) {
            if let lesson = selectedLesson {
                LessonDetailView(lesson: lesson, student: nil)
            }
        }
    }

    private var subjectHeaderView: some View {
        VStack(spacing: 15) {
            Text("Grade \(gradeLevel.displayName) Subjects")
                .font(.title2)
                .fontWeight(.semibold)

            Text("Choose a subject to explore lessons and activities")
                .font(.subheadline)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
        }
    }

    private var allSubjectsView: some View {
        VStack(alignment: .leading, spacing: 20) {
            HStack {
                Image(systemName: "graduationcap.fill")
                    .font(.title2)
                    .foregroundColor(.blue)
                    .scaleEffect(1.0)
                    .animation(.easeInOut(duration: 2).repeatForever(autoreverses: true), value: true)

                Text("Available Subjects")
                    .font(.headline)
                    .fontWeight(.semibold)
                    .foregroundStyle(
                        LinearGradient(
                            colors: [.blue, .purple],
                            startPoint: .leading,
                            endPoint: .trailing
                        )
                    )

                Spacer()

                Text("\(filteredSubjects.count) subjects")
                    .font(.caption)
                    .padding(.horizontal, 12)
                    .padding(.vertical, 4)
                    .background(.blue.opacity(0.1))
                    .foregroundColor(.blue)
                    .cornerRadius(12)
            }

            LazyVGrid(columns: [
                GridItem(.flexible()),
                GridItem(.flexible())
            ], spacing: 20) {
                ForEach(Array(filteredSubjects.enumerated()), id: \.element.id) { index, subject in
                    SubjectCard(
                        subject: subject,
                        gradeLevel: gradeLevel,
                        lessonCount: lessonDataService.getLessonsForSubject(subject.id, gradeLevel: gradeLevel).count
                    ) {
                        // Navigate to subject lessons with haptic feedback
                        let impactFeedback = UIImpactFeedbackGenerator(style: .medium)
                        impactFeedback.impactOccurred()

                        withAnimation(.spring(response: 0.6, dampingFraction: 0.8)) {
                            self.selectedLesson = lessonsForSubject.first
                            showingLessonDetail = true
                        }
                    }
                    .scaleEffect(1.0)
                    .animation(
                        .spring(response: 0.6, dampingFraction: 0.8)
                        .delay(Double(index) * 0.1),
                        value: filteredSubjects.count
                    )
                }
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 20)
                .fill(.ultraThinMaterial)
                .shadow(color: .blue.opacity(0.1), radius: 10, x: 0, y: 5)
        )
    }

    private var lessonsView: some View {
        VStack(alignment: .leading, spacing: 15) {
            Text("Lessons in \(subject.name)")
                .font(.headline)
                .fontWeight(.semibold)

            LazyVStack(spacing: 12) {
                ForEach(lessonsForSubject) { lesson in
                    LessonCard(lesson: lesson, student: nil) {
                        selectedLesson = lesson
                        showingLessonDetail = true
                    }
                }
            }
        }
    }
}

struct SubjectCard: View {
    let subject: Subject
    let gradeLevel: GradeLevel
    let lessonCount: Int
    let action: () -> Void

    @State private var isPressed = false
    @State private var isHovered = false

    var body: some View {
        Button(action: action) {
            VStack(spacing: 18) {
                // Subject icon with animated background
                ZStack {
                    Circle()
                        .fill(
                            LinearGradient(
                                colors: [subject.color.opacity(0.2), subject.color.opacity(0.4)],
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            )
                        )
                        .frame(width: 70, height: 70)
                        .scaleEffect(isHovered ? 1.1 : 1.0)
                        .animation(.spring(response: 0.4, dampingFraction: 0.6), value: isHovered)

                    Image(systemName: subject.icon)
                        .font(.title)
                        .foregroundStyle(
                            LinearGradient(
                                colors: [subject.color, subject.color.opacity(0.7)],
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            )
                        )
                        .scaleEffect(isPressed ? 0.9 : 1.0)
                        .animation(.spring(response: 0.3, dampingFraction: 0.7), value: isPressed)
                }

                // Content with enhanced styling
                VStack(spacing: 10) {
                    Text(subject.name)
                        .font(.headline)
                        .fontWeight(.bold)
                        .foregroundStyle(
                            LinearGradient(
                                colors: [.primary, .secondary],
                                startPoint: .leading,
                                endPoint: .trailing
                            )
                        )
                        .multilineTextAlignment(.center)

                    Text(subject.subjectDescription)
                        .font(.caption)
                        .foregroundColor(.secondary)
                        .multilineTextAlignment(.center)
                        .lineLimit(2)
                        .opacity(0.8)

                    HStack(spacing: 8) {
                        Image(systemName: "book.fill")
                            .font(.caption2)
                            .foregroundColor(subject.color)

                        Text("\(lessonCount) lessons")
                            .font(.caption)
                            .fontWeight(.medium)
                            .foregroundColor(subject.color)
                    }
                    .padding(.horizontal, 12)
                    .padding(.vertical, 4)
                    .background(subject.color.opacity(0.1))
                    .cornerRadius(8)
                }

                // Enhanced action button
                HStack(spacing: 6) {
                    Image(systemName: "play.fill")
                        .font(.caption)

                    Text("Start Learning")
                        .font(.caption)
                        .fontWeight(.semibold)
                }
                .foregroundColor(.white)
                .padding(.horizontal, 20)
                .padding(.vertical, 8)
                .background(
                    LinearGradient(
                        colors: [subject.color, subject.color.opacity(0.8)],
                        startPoint: .leading,
                        endPoint: .trailing
                    )
                )
                .cornerRadius(16)
                .shadow(color: subject.color.opacity(0.3), radius: 4, x: 0, y: 2)
                .scaleEffect(isPressed ? 0.95 : 1.0)
                .animation(.spring(response: 0.3, dampingFraction: 0.7), value: isPressed)
            }
            .padding(20)
            .background(
                RoundedRectangle(cornerRadius: 20)
                    .fill(.ultraThinMaterial)
                    .overlay(
                        RoundedRectangle(cornerRadius: 20)
                            .stroke(
                                LinearGradient(
                                    colors: [subject.color.opacity(0.3), subject.color.opacity(0.1)],
                                    startPoint: .topLeading,
                                    endPoint: .bottomTrailing
                                ),
                                lineWidth: 1
                            )
                    )
                    .shadow(color: subject.color.opacity(0.1), radius: 8, x: 0, y: 4)
            )
            .scaleEffect(isPressed ? 0.98 : 1.0)
            .animation(.spring(response: 0.4, dampingFraction: 0.6), value: isPressed)
        }
        .buttonStyle(PlainButtonStyle())
        .onLongPressGesture(minimumDuration: 0, maximumDistance: .infinity, pressing: { pressing in
            isPressed = pressing
        }, perform: {})
        .onHover { hovering in
            isHovered = hovering
        }
    }
}



#Preview {
    SubjectDetailView(
        subject: Subject(
            name: "Mathematics",
            code: "MATH",
            category: .core,
            schoolLevels: [.elementary],
            gradeLevels: [.grade3],
            description: "Numbers, calculations, and problem solving"
        ),
        gradeLevel: .grade3
    )
}
